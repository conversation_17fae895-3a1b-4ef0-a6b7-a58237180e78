//+------------------------------------------------------------------+
//|                                                     SampleEA.mq5 |
//|                                  Copyright 2025, Your Company    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input double LotSize = 0.1;
input int StopLoss = 100;
input int TakeProfit = 200;
input int MagicNumber = 12345;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("SampleEA initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("SampleEA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Sample trading logic
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(Symbol(), Period(), 0);
    
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        
        // Simple moving average crossover strategy
        double ma_fast = iMA(Symbol(), Period(), 10, 0, MODE_SMA, PRICE_CLOSE, 1);
        double ma_slow = iMA(Symbol(), Period(), 20, 0, MODE_SMA, PRICE_CLOSE, 1);
        double ma_fast_prev = iMA(Symbol(), Period(), 10, 0, MODE_SMA, PRICE_CLOSE, 2);
        double ma_slow_prev = iMA(Symbol(), Period(), 20, 0, MODE_SMA, PRICE_CLOSE, 2);
        
        // Check for buy signal
        if(ma_fast > ma_slow && ma_fast_prev <= ma_slow_prev)
        {
            if(PositionsTotal() == 0)
            {
                OpenPosition(ORDER_TYPE_BUY);
            }
        }
        
        // Check for sell signal
        if(ma_fast < ma_slow && ma_fast_prev >= ma_slow_prev)
        {
            if(PositionsTotal() == 0)
            {
                OpenPosition(ORDER_TYPE_SELL);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open position function                                           |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE order_type)
{
    MqlTradeRequest request;
    MqlTradeResult result;
    
    ZeroMemory(request);
    ZeroMemory(result);
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = order_type;
    request.price = (order_type == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    request.deviation = 3;
    request.magic = MagicNumber;
    
    // Set stop loss and take profit
    if(order_type == ORDER_TYPE_BUY)
    {
        request.sl = request.price - StopLoss * Point();
        request.tp = request.price + TakeProfit * Point();
    }
    else
    {
        request.sl = request.price + StopLoss * Point();
        request.tp = request.price - TakeProfit * Point();
    }
    
    if(OrderSend(request, result))
    {
        Print("Order placed successfully. Ticket: ", result.order);
    }
    else
    {
        Print("Failed to place order. Error: ", GetLastError());
    }
}
