//+------------------------------------------------------------------+
//|                                             SampleIndicator.mq5 |
//|                                  Copyright 2025, Your Company    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot settings
#property indicator_label1  "Fast MA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "Slow MA"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

//--- Input parameters
input int FastPeriod = 10;
input int SlowPeriod = 20;
input ENUM_MA_METHOD MAMethod = MODE_SMA;
input ENUM_APPLIED_PRICE AppliedPrice = PRICE_CLOSE;

//--- Indicator buffers
double FastMABuffer[];
double SlowMABuffer[];

//--- Handles
int fast_ma_handle;
int slow_ma_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set indicator buffers
    SetIndexBuffer(0, FastMABuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SlowMABuffer, INDICATOR_DATA);
    
    //--- Create MA handles
    fast_ma_handle = iMA(Symbol(), Period(), FastPeriod, 0, MAMethod, AppliedPrice);
    slow_ma_handle = iMA(Symbol(), Period(), SlowPeriod, 0, MAMethod, AppliedPrice);
    
    if(fast_ma_handle == INVALID_HANDLE || slow_ma_handle == INVALID_HANDLE)
    {
        Print("Failed to create MA handles");
        return(INIT_FAILED);
    }
    
    //--- Set indicator properties
    IndicatorSetString(INDICATOR_SHORTNAME, "Sample MA Cross");
    IndicatorSetInteger(INDICATOR_DIGITS, Digits());
    
    Print("SampleIndicator initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for minimum bars
    if(rates_total < SlowPeriod)
        return(0);
    
    //--- Calculate start position
    int start = prev_calculated;
    if(start == 0)
        start = SlowPeriod - 1;
    
    //--- Copy MA values
    if(CopyBuffer(fast_ma_handle, 0, 0, rates_total, FastMABuffer) <= 0)
        return(0);
    
    if(CopyBuffer(slow_ma_handle, 0, 0, rates_total, SlowMABuffer) <= 0)
        return(0);
    
    //--- Return value of prev_calculated for next call
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Indicator deinitialization function                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release handles
    if(fast_ma_handle != INVALID_HANDLE)
        IndicatorRelease(fast_ma_handle);
    if(slow_ma_handle != INVALID_HANDLE)
        IndicatorRelease(slow_ma_handle);
    
    Print("SampleIndicator deinitialized");
}
