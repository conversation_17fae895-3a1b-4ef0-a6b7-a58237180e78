//+------------------------------------------------------------------+
//|                                                     test_lh.mq5 |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property script_show_inputs

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   // Check if the LittleHelper indicator is installed
   if(IndicatorExists("LittleHelper"))
   {
      Print("LittleHelper indicator is installed and can be used.");
      
      // Try to create a chart indicator
      long chart = ChartID();
      int window = ChartWindowFind(chart, "LittleHelper");
      
      if(window == -1)
      {
         Print("Adding LittleHelper to chart...");
         if(ChartIndicatorAdd(chart, 1, "LittleHelper"))
            Print("LittleHelper added successfully!");
         else
            Print("Failed to add LittleHelper. Error: ", GetLastError());
      }
      else
      {
         Print("LittleHelper is already on the chart in window ", window);
      }
   }
   else
   {
      Print("LittleHelper indicator is not installed or cannot be found.");
   }
}

//+------------------------------------------------------------------+
//| Check if an indicator exists                                     |
//+------------------------------------------------------------------+
bool IndicatorExists(string name)
{
   // Try to create the indicator handle
   int handle = iCustom(Symbol(), Period(), name);
   
   // Check if the handle is valid
   if(handle != INVALID_HANDLE)
   {
      // Release the handle
      IndicatorRelease(handle);
      return true;
   }
   
   return false;
}
//+------------------------------------------------------------------+
