# MQL5 Development Quick Reference

## Development Tools
- `./mql5_dev_tools.sh` - Main development script
- `./log_monitor.py` - Log monitoring utility

## Common Commands
```bash
# Compile code
./mql5_dev_tools.sh compile MQL5/Experts/Custom/MyEA.mq5

# Start applications
./mql5_dev_tools.sh run-terminal
./mql5_dev_tools.sh run-editor
./mql5_dev_tools.sh run-tester

# Monitor logs
./log_monitor.py monitor terminal
./log_monitor.py monitor editor
./log_monitor.py search "error"

# Test Expert Advisor
./mql5_dev_tools.sh test-ea "Experts/Custom/SampleEA.ex5"
```

## Directory Structure
- `MQL5/Experts/Custom/` - Your Expert Advisors
- `MQL5/Indicators/Custom/` - Your indicators
- `MQL5/Scripts/Custom/` - Your scripts
- `logs/` - Terminal and compilation logs
- `Tester/logs/` - Strategy tester logs

## Log Files
- `logs/YYYYMMDD.log` - Daily terminal logs
- `logs/metaeditor.log` - Compilation logs
- `Tester/logs/YYYYMMDD.log` - Tester logs

## Wine Environment
- Wine Prefix: `/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5`
- Architecture: win64
- Debug: Disabled for performance

## Troubleshooting
1. If compilation fails, check `logs/metaeditor.log`
2. If terminal crashes, check terminal logs
3. Use `./mql5_dev_tools.sh status` for environment check
4. Logs are UTF-16 encoded - use log_monitor.py to read them
