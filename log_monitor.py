#!/usr/bin/env python3
"""
MQL5 Log Monitor for MetaTrader 5 Wine Environment
Handles UTF-16 encoded logs and provides real-time monitoring
"""

import os
import sys
import time
import argparse
from datetime import datetime
from pathlib import Path

class MQL5LogMonitor:
    def __init__(self, mt5_path):
        self.mt5_path = Path(mt5_path)
        self.logs_dir = self.mt5_path / "logs"
        self.tester_logs_dir = self.mt5_path / "Tester" / "logs"
        
    def decode_utf16_log(self, log_path):
        """Decode UTF-16LE encoded log file"""
        try:
            with open(log_path, 'rb') as f:
                content = f.read()
            
            # Try UTF-16LE first (most common for MT5)
            try:
                decoded = content.decode('utf-16le')
                return decoded
            except UnicodeDecodeError:
                # Fallback to UTF-16BE
                try:
                    decoded = content.decode('utf-16be')
                    return decoded
                except UnicodeDecodeError:
                    # Last resort: ignore errors
                    decoded = content.decode('utf-16le', errors='ignore')
                    return decoded
        except Exception as e:
            return f"Error reading log file: {e}"
    
    def get_today_log(self):
        """Get today's terminal log file"""
        today = datetime.now().strftime("%Y%m%d")
        log_file = self.logs_dir / f"{today}.log"
        return log_file if log_file.exists() else None
    
    def get_metaeditor_log(self):
        """Get MetaEditor log file"""
        log_file = self.logs_dir / "metaeditor.log"
        return log_file if log_file.exists() else None
    
    def get_tester_log(self):
        """Get today's tester log file"""
        today = datetime.now().strftime("%Y%m%d")
        log_file = self.tester_logs_dir / f"{today}.log"
        return log_file if log_file.exists() else None
    
    def parse_log_line(self, line):
        """Parse a log line and extract components"""
        line = line.strip()
        if not line:
            return None
        
        # MT5 log format: CODE\tLEVEL\tTIME\tCATEGORY\tMESSAGE
        parts = line.split('\t')
        if len(parts) >= 4:
            return {
                'code': parts[0],
                'level': parts[1],
                'time': parts[2],
                'category': parts[3],
                'message': '\t'.join(parts[4:]) if len(parts) > 4 else ''
            }
        return {'raw': line}
    
    def format_log_entry(self, entry):
        """Format log entry for display"""
        if 'raw' in entry:
            return entry['raw']
        
        time_str = entry.get('time', '')
        category = entry.get('category', '')
        message = entry.get('message', '')
        
        return f"[{time_str}] {category}: {message}"
    
    def monitor_log(self, log_path, follow=True):
        """Monitor a log file with real-time updates"""
        if not log_path or not log_path.exists():
            print(f"Log file not found: {log_path}")
            return
        
        print(f"Monitoring: {log_path}")
        print("=" * 60)
        
        # Read existing content
        content = self.decode_utf16_log(log_path)
        lines = content.split('\n')
        
        # Display recent lines
        for line in lines[-20:]:  # Show last 20 lines
            if line.strip():
                entry = self.parse_log_line(line)
                if entry:
                    print(self.format_log_entry(entry))
        
        if not follow:
            return
        
        # Monitor for new content
        last_size = log_path.stat().st_size
        print("\n--- Monitoring for new entries (Ctrl+C to stop) ---")
        
        try:
            while True:
                current_size = log_path.stat().st_size
                if current_size > last_size:
                    # File has grown, read new content
                    content = self.decode_utf16_log(log_path)
                    lines = content.split('\n')
                    
                    # Find new lines
                    for line in lines:
                        if line.strip():
                            entry = self.parse_log_line(line)
                            if entry:
                                formatted = self.format_log_entry(entry)
                                if formatted not in [self.format_log_entry(self.parse_log_line(l)) for l in lines[-20:]]:
                                    print(f"[NEW] {formatted}")
                    
                    last_size = current_size
                
                time.sleep(1)
        except KeyboardInterrupt:
            print("\nMonitoring stopped.")
    
    def list_available_logs(self):
        """List all available log files"""
        print("Available Log Files:")
        print("=" * 40)
        
        # Terminal logs
        print("\nTerminal Logs:")
        if self.logs_dir.exists():
            for log_file in sorted(self.logs_dir.glob("*.log")):
                if log_file.name != "metaeditor.log":
                    size = log_file.stat().st_size
                    modified = datetime.fromtimestamp(log_file.stat().st_mtime)
                    print(f"  {log_file.name} ({size} bytes, modified: {modified})")
        
        # MetaEditor log
        print("\nMetaEditor Log:")
        metaeditor_log = self.get_metaeditor_log()
        if metaeditor_log:
            size = metaeditor_log.stat().st_size
            modified = datetime.fromtimestamp(metaeditor_log.stat().st_mtime)
            print(f"  metaeditor.log ({size} bytes, modified: {modified})")
        else:
            print("  No MetaEditor log found")
        
        # Tester logs
        print("\nTester Logs:")
        if self.tester_logs_dir.exists():
            for log_file in sorted(self.tester_logs_dir.glob("*.log")):
                size = log_file.stat().st_size
                modified = datetime.fromtimestamp(log_file.stat().st_mtime)
                print(f"  {log_file.name} ({size} bytes, modified: {modified})")
        else:
            print("  No tester logs found")
    
    def search_logs(self, search_term, log_type="all"):
        """Search for specific terms in logs"""
        print(f"Searching for '{search_term}' in {log_type} logs...")
        print("=" * 60)
        
        logs_to_search = []
        
        if log_type in ["all", "terminal"]:
            if self.logs_dir.exists():
                logs_to_search.extend(self.logs_dir.glob("*.log"))
        
        if log_type in ["all", "tester"]:
            if self.tester_logs_dir.exists():
                logs_to_search.extend(self.tester_logs_dir.glob("*.log"))
        
        found_entries = []
        
        for log_path in logs_to_search:
            if log_path.name == "metaeditor.log" and log_type == "terminal":
                continue
                
            content = self.decode_utf16_log(log_path)
            lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                if search_term.lower() in line.lower():
                    entry = self.parse_log_line(line)
                    if entry:
                        found_entries.append({
                            'file': log_path.name,
                            'line': line_num,
                            'content': self.format_log_entry(entry)
                        })
        
        if found_entries:
            for entry in found_entries[-50:]:  # Show last 50 matches
                print(f"{entry['file']}:{entry['line']} - {entry['content']}")
        else:
            print("No matches found.")

def main():
    parser = argparse.ArgumentParser(description="MQL5 Log Monitor for MetaTrader 5")
    parser.add_argument("--mt5-path", 
                       default="/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5/drive_c/Program Files/MetaTrader 5",
                       help="Path to MetaTrader 5 installation")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Monitor command
    monitor_parser = subparsers.add_parser("monitor", help="Monitor log files")
    monitor_parser.add_argument("type", choices=["terminal", "editor", "tester"], 
                               help="Type of log to monitor")
    monitor_parser.add_argument("--no-follow", action="store_true", 
                               help="Don't follow log file for new entries")
    
    # List command
    subparsers.add_parser("list", help="List available log files")
    
    # Search command
    search_parser = subparsers.add_parser("search", help="Search in log files")
    search_parser.add_argument("term", help="Search term")
    search_parser.add_argument("--type", choices=["all", "terminal", "tester"], 
                              default="all", help="Log type to search in")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    monitor = MQL5LogMonitor(args.mt5_path)
    
    if args.command == "monitor":
        if args.type == "terminal":
            log_path = monitor.get_today_log()
        elif args.type == "editor":
            log_path = monitor.get_metaeditor_log()
        elif args.type == "tester":
            log_path = monitor.get_tester_log()
        
        monitor.monitor_log(log_path, follow=not args.no_follow)
    
    elif args.command == "list":
        monitor.list_available_logs()
    
    elif args.command == "search":
        monitor.search_logs(args.term, args.type)

if __name__ == "__main__":
    main()
