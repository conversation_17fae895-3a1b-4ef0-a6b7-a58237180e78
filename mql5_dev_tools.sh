#!/bin/bash

# MQL5 Development Tools for Wine Environment
# Usage: ./mql5_dev_tools.sh [command] [options]

MT5_PATH="/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5/drive_c/Program Files/MetaTrader 5"
WINE_CMD="wine64"

# Set Wine environment
export WINEPREFIX="/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5"
export WINEARCH=win64
export WINEDEBUG=-all

cd "$MT5_PATH"

case "$1" in
    "compile")
        if [ -z "$2" ]; then
            echo "Usage: $0 compile <mq5_file_path>"
            echo "Example: $0 compile MQL5/Experts/MyEA.mq5"
            exit 1
        fi
        echo "Compiling $2..."
        $WINE_CMD metaeditor64.exe /compile:"$2"
        echo "Check logs/metaeditor.log for compilation results"
        ;;
    
    "run-terminal")
        echo "Starting MetaTrader 5 Terminal..."
        $WINE_CMD terminal64.exe &
        ;;
    
    "run-editor")
        echo "Starting MetaEditor..."
        $WINE_CMD metaeditor64.exe &
        ;;
    
    "run-tester")
        echo "Starting Strategy Tester..."
        $WINE_CMD metatester64.exe &
        ;;
    
    "monitor-logs")
        echo "Monitoring terminal logs (Ctrl+C to stop)..."
        tail -f "logs/$(date +%Y%m%d).log" 2>/dev/null || echo "No log file for today yet"
        ;;
    
    "monitor-compile")
        echo "Monitoring compilation logs (Ctrl+C to stop)..."
        tail -f "logs/metaeditor.log"
        ;;
    
    "clean-logs")
        echo "Cleaning old log files..."
        find logs/ -name "*.log" -mtime +30 -delete
        echo "Logs older than 30 days removed"
        ;;
    
    "backup-mql5")
        BACKUP_DIR="$HOME/MT5_Backup_$(date +%Y%m%d_%H%M%S)"
        echo "Backing up MQL5 directory to $BACKUP_DIR..."
        cp -r MQL5/ "$BACKUP_DIR"
        echo "Backup completed"
        ;;
    
    "list-experts")
        echo "Available Expert Advisors:"
        find MQL5/Experts/ -name "*.ex5" -o -name "*.mq5" | sort
        ;;
    
    "list-indicators")
        echo "Available Indicators:"
        find MQL5/Indicators/ -name "*.ex5" -o -name "*.mq5" | sort
        ;;
    
    "test-ea")
        if [ -z "$2" ]; then
            echo "Usage: $0 test-ea <expert_path> [symbol] [period] [from_date] [to_date]"
            echo "Example: $0 test-ea 'Experts/Examples/Moving Average/Moving Average.ex5' EURUSD H1 2024.01.01 2024.12.31"
            exit 1
        fi
        
        EXPERT="$2"
        SYMBOL="${3:-EURUSD}"
        PERIOD="${4:-H1}"
        FROM="${5:-2024.01.01}"
        TO="${6:-2024.12.31}"
        
        echo "Testing Expert: $EXPERT"
        echo "Symbol: $SYMBOL, Period: $PERIOD"
        echo "Date Range: $FROM to $TO"
        
        $WINE_CMD metatester64.exe /expert:"$EXPERT" /symbol:"$SYMBOL" /period:"$PERIOD" /from:"$FROM" /to:"$TO"
        ;;
    
    "decode-log")
        if [ -z "$2" ]; then
            echo "Usage: $0 decode-log <log_file>"
            echo "Converts UTF-16 encoded logs to readable format"
            exit 1
        fi
        
        if [ -f "$2" ]; then
            iconv -f UTF-16LE -t UTF-8 "$2" | head -50
        else
            echo "Log file not found: $2"
        fi
        ;;
    
    "status")
        echo "=== MetaTrader 5 Development Environment Status ==="
        echo "MT5 Path: $MT5_PATH"
        echo "Wine Command: $WINE_CMD"
        echo ""
        echo "Available Executables:"
        ls -la *.exe 2>/dev/null || echo "No executables found"
        echo ""
        echo "Recent Logs:"
        ls -la logs/*.log 2>/dev/null | tail -5 || echo "No log files found"
        echo ""
        echo "MQL5 Structure:"
        ls -la MQL5/ 2>/dev/null || echo "MQL5 directory not found"
        ;;
    
    "help"|*)
        echo "MQL5 Development Tools for Wine Environment"
        echo ""
        echo "Available commands:"
        echo "  compile <file>     - Compile MQL5 source file"
        echo "  run-terminal       - Start MetaTrader 5 Terminal"
        echo "  run-editor         - Start MetaEditor"
        echo "  run-tester         - Start Strategy Tester"
        echo "  monitor-logs       - Monitor terminal logs in real-time"
        echo "  monitor-compile    - Monitor compilation logs"
        echo "  clean-logs         - Remove old log files"
        echo "  backup-mql5        - Backup MQL5 directory"
        echo "  list-experts       - List available Expert Advisors"
        echo "  list-indicators    - List available Indicators"
        echo "  test-ea <expert>   - Run strategy test on Expert Advisor"
        echo "  decode-log <file>  - Decode UTF-16 log file to readable format"
        echo "  status             - Show environment status"
        echo "  help               - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 compile MQL5/Experts/MyEA.mq5"
        echo "  $0 test-ea 'Experts/Examples/Moving Average/Moving Average.ex5'"
        echo "  $0 monitor-logs"
        ;;
esac
