#!/bin/bash

# MQL5 Development Environment Setup Script
# Sets up a complete development workflow for MetaTrader 5 in Wine on macOS

MT5_PATH="/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5/drive_c/Program Files/MetaTrader 5"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== MQL5 Development Environment Setup ==="
echo "MT5 Path: $MT5_PATH"
echo ""

# Check if MT5 directory exists
if [ ! -d "$MT5_PATH" ]; then
    echo "❌ MetaTrader 5 directory not found at: $MT5_PATH"
    echo "Please verify your MetaTrader 5 installation path."
    exit 1
fi

cd "$MT5_PATH"

# Check Wine installation
echo "🔍 Checking Wine installation..."
if ! command -v wine64 &> /dev/null; then
    echo "❌ Wine64 not found. Please install Wine first."
    echo "Install with: brew install wine-stable"
    exit 1
fi
echo "✅ Wine64 found: $(which wine64)"

# Check Python installation
echo "🔍 Checking Python installation..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python first."
    exit 1
fi
echo "✅ Python3 found: $(which python3)"

# Set up Wine environment
echo "🍷 Setting up Wine environment..."
export WINEPREFIX="/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5"
export WINEARCH=win64
export WINEDEBUG=-all

# Verify MT5 executables
echo "🔍 Verifying MetaTrader 5 executables..."
EXECUTABLES=("terminal64.exe" "metaeditor64.exe" "metatester64.exe")
for exe in "${EXECUTABLES[@]}"; do
    if [ -f "$exe" ]; then
        echo "✅ Found: $exe"
    else
        echo "❌ Missing: $exe"
    fi
done

# Create development directories if they don't exist
echo "📁 Setting up development directories..."
DIRS=("MQL5/Experts/Custom" "MQL5/Indicators/Custom" "MQL5/Scripts/Custom" "MQL5/Libraries/Custom" "dev_backup" "dev_logs")
for dir in "${DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "✅ Created: $dir"
    else
        echo "✅ Exists: $dir"
    fi
done

# Copy development tools to MT5 directory
echo "🛠️  Installing development tools..."
if [ -f "$SCRIPT_DIR/mql5_dev_tools.sh" ]; then
    cp "$SCRIPT_DIR/mql5_dev_tools.sh" .
    chmod +x mql5_dev_tools.sh
    echo "✅ Installed: mql5_dev_tools.sh"
else
    echo "❌ mql5_dev_tools.sh not found in script directory"
fi

if [ -f "$SCRIPT_DIR/log_monitor.py" ]; then
    cp "$SCRIPT_DIR/log_monitor.py" .
    chmod +x log_monitor.py
    echo "✅ Installed: log_monitor.py"
else
    echo "❌ log_monitor.py not found in script directory"
fi

# Create development aliases
echo "🔗 Creating development aliases..."
ALIAS_FILE="$HOME/.mql5_aliases"
cat > "$ALIAS_FILE" << 'EOF'
# MQL5 Development Aliases
alias mql5-dev='cd "/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5/drive_c/Program Files/MetaTrader 5"'
alias mql5-compile='./mql5_dev_tools.sh compile'
alias mql5-terminal='./mql5_dev_tools.sh run-terminal'
alias mql5-editor='./mql5_dev_tools.sh run-editor'
alias mql5-tester='./mql5_dev_tools.sh run-tester'
alias mql5-logs='./log_monitor.py monitor terminal'
alias mql5-compile-logs='./log_monitor.py monitor editor'
alias mql5-test-logs='./log_monitor.py monitor tester'
alias mql5-status='./mql5_dev_tools.sh status'
alias mql5-help='./mql5_dev_tools.sh help'
EOF

echo "✅ Created aliases file: $ALIAS_FILE"
echo "   Add 'source $ALIAS_FILE' to your ~/.bashrc or ~/.zshrc"

# Create sample Expert Advisor
echo "📝 Creating sample Expert Advisor..."
SAMPLE_EA="MQL5/Experts/Custom/SampleEA.mq5"
cat > "$SAMPLE_EA" << 'EOF'
//+------------------------------------------------------------------+
//|                                                     SampleEA.mq5 |
//|                                  Copyright 2025, Your Company    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters
input double LotSize = 0.1;
input int StopLoss = 100;
input int TakeProfit = 200;
input int MagicNumber = 12345;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("SampleEA initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("SampleEA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Sample trading logic
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(Symbol(), Period(), 0);
    
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        
        // Simple moving average crossover strategy
        double ma_fast = iMA(Symbol(), Period(), 10, 0, MODE_SMA, PRICE_CLOSE, 1);
        double ma_slow = iMA(Symbol(), Period(), 20, 0, MODE_SMA, PRICE_CLOSE, 1);
        double ma_fast_prev = iMA(Symbol(), Period(), 10, 0, MODE_SMA, PRICE_CLOSE, 2);
        double ma_slow_prev = iMA(Symbol(), Period(), 20, 0, MODE_SMA, PRICE_CLOSE, 2);
        
        // Check for buy signal
        if(ma_fast > ma_slow && ma_fast_prev <= ma_slow_prev)
        {
            if(PositionsTotal() == 0)
            {
                OpenPosition(ORDER_TYPE_BUY);
            }
        }
        
        // Check for sell signal
        if(ma_fast < ma_slow && ma_fast_prev >= ma_slow_prev)
        {
            if(PositionsTotal() == 0)
            {
                OpenPosition(ORDER_TYPE_SELL);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Open position function                                           |
//+------------------------------------------------------------------+
void OpenPosition(ENUM_ORDER_TYPE order_type)
{
    MqlTradeRequest request;
    MqlTradeResult result;
    
    ZeroMemory(request);
    ZeroMemory(result);
    
    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = LotSize;
    request.type = order_type;
    request.price = (order_type == ORDER_TYPE_BUY) ? SymbolInfoDouble(Symbol(), SYMBOL_ASK) : SymbolInfoDouble(Symbol(), SYMBOL_BID);
    request.deviation = 3;
    request.magic = MagicNumber;
    
    // Set stop loss and take profit
    if(order_type == ORDER_TYPE_BUY)
    {
        request.sl = request.price - StopLoss * Point();
        request.tp = request.price + TakeProfit * Point();
    }
    else
    {
        request.sl = request.price + StopLoss * Point();
        request.tp = request.price - TakeProfit * Point();
    }
    
    if(OrderSend(request, result))
    {
        Print("Order placed successfully. Ticket: ", result.order);
    }
    else
    {
        Print("Failed to place order. Error: ", GetLastError());
    }
}
EOF

echo "✅ Created sample Expert Advisor: $SAMPLE_EA"

# Create sample indicator
echo "📊 Creating sample indicator..."
SAMPLE_IND="MQL5/Indicators/Custom/SampleIndicator.mq5"
cat > "$SAMPLE_IND" << 'EOF'
//+------------------------------------------------------------------+
//|                                             SampleIndicator.mq5 |
//|                                  Copyright 2025, Your Company    |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_buffers 2
#property indicator_plots   2

//--- Plot settings
#property indicator_label1  "Fast MA"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrBlue
#property indicator_style1  STYLE_SOLID
#property indicator_width1  1

#property indicator_label2  "Slow MA"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  1

//--- Input parameters
input int FastPeriod = 10;
input int SlowPeriod = 20;
input ENUM_MA_METHOD MAMethod = MODE_SMA;
input ENUM_APPLIED_PRICE AppliedPrice = PRICE_CLOSE;

//--- Indicator buffers
double FastMABuffer[];
double SlowMABuffer[];

//--- Handles
int fast_ma_handle;
int slow_ma_handle;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set indicator buffers
    SetIndexBuffer(0, FastMABuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SlowMABuffer, INDICATOR_DATA);
    
    //--- Create MA handles
    fast_ma_handle = iMA(Symbol(), Period(), FastPeriod, 0, MAMethod, AppliedPrice);
    slow_ma_handle = iMA(Symbol(), Period(), SlowPeriod, 0, MAMethod, AppliedPrice);
    
    if(fast_ma_handle == INVALID_HANDLE || slow_ma_handle == INVALID_HANDLE)
    {
        Print("Failed to create MA handles");
        return(INIT_FAILED);
    }
    
    //--- Set indicator properties
    IndicatorSetString(INDICATOR_SHORTNAME, "Sample MA Cross");
    IndicatorSetInteger(INDICATOR_DIGITS, Digits());
    
    Print("SampleIndicator initialized successfully");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for minimum bars
    if(rates_total < SlowPeriod)
        return(0);
    
    //--- Calculate start position
    int start = prev_calculated;
    if(start == 0)
        start = SlowPeriod - 1;
    
    //--- Copy MA values
    if(CopyBuffer(fast_ma_handle, 0, 0, rates_total, FastMABuffer) <= 0)
        return(0);
    
    if(CopyBuffer(slow_ma_handle, 0, 0, rates_total, SlowMABuffer) <= 0)
        return(0);
    
    //--- Return value of prev_calculated for next call
    return(rates_total);
}

//+------------------------------------------------------------------+
//| Indicator deinitialization function                             |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release handles
    if(fast_ma_handle != INVALID_HANDLE)
        IndicatorRelease(fast_ma_handle);
    if(slow_ma_handle != INVALID_HANDLE)
        IndicatorRelease(slow_ma_handle);
    
    Print("SampleIndicator deinitialized");
}
EOF

echo "✅ Created sample indicator: $SAMPLE_IND"

# Test compilation
echo "🔨 Testing compilation..."
echo "Compiling sample Expert Advisor..."
wine64 metaeditor64.exe /compile:"MQL5/Experts/Custom/SampleEA.mq5" 2>/dev/null &
sleep 3

echo "Compiling sample indicator..."
wine64 metaeditor64.exe /compile:"MQL5/Indicators/Custom/SampleIndicator.mq5" 2>/dev/null &
sleep 3

# Create quick reference
echo "📚 Creating quick reference..."
cat > "MQL5_DEV_REFERENCE.md" << 'EOF'
# MQL5 Development Quick Reference

## Development Tools
- `./mql5_dev_tools.sh` - Main development script
- `./log_monitor.py` - Log monitoring utility

## Common Commands
```bash
# Compile code
./mql5_dev_tools.sh compile MQL5/Experts/Custom/MyEA.mq5

# Start applications
./mql5_dev_tools.sh run-terminal
./mql5_dev_tools.sh run-editor
./mql5_dev_tools.sh run-tester

# Monitor logs
./log_monitor.py monitor terminal
./log_monitor.py monitor editor
./log_monitor.py search "error"

# Test Expert Advisor
./mql5_dev_tools.sh test-ea "Experts/Custom/SampleEA.ex5"
```

## Directory Structure
- `MQL5/Experts/Custom/` - Your Expert Advisors
- `MQL5/Indicators/Custom/` - Your indicators
- `MQL5/Scripts/Custom/` - Your scripts
- `logs/` - Terminal and compilation logs
- `Tester/logs/` - Strategy tester logs

## Log Files
- `logs/YYYYMMDD.log` - Daily terminal logs
- `logs/metaeditor.log` - Compilation logs
- `Tester/logs/YYYYMMDD.log` - Tester logs

## Wine Environment
- Wine Prefix: `/Users/<USER>/Library/Application Support/net.metaquotes.wine.metatrader5`
- Architecture: win64
- Debug: Disabled for performance

## Troubleshooting
1. If compilation fails, check `logs/metaeditor.log`
2. If terminal crashes, check terminal logs
3. Use `./mql5_dev_tools.sh status` for environment check
4. Logs are UTF-16 encoded - use log_monitor.py to read them
EOF

echo "✅ Created reference: MQL5_DEV_REFERENCE.md"

echo ""
echo "🎉 MQL5 Development Environment Setup Complete!"
echo ""
echo "Next steps:"
echo "1. Add aliases to your shell: source $ALIAS_FILE"
echo "2. Test compilation: ./mql5_dev_tools.sh compile MQL5/Experts/Custom/SampleEA.mq5"
echo "3. Start terminal: ./mql5_dev_tools.sh run-terminal"
echo "4. Monitor logs: ./log_monitor.py monitor terminal"
echo ""
echo "For help: ./mql5_dev_tools.sh help"
echo "Quick reference: cat MQL5_DEV_REFERENCE.md"
